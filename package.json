{"name": "api-upload", "version": "1.0.0", "private": true, "type": "module", "main": "src/index.ts", "scripts": {"predev": "npm run db:migrate:local", "dev": "wrangler dev", "deploy": "wrangler deploy", "test": "vitest", "test:watch": "vitest --watch", "type-check": "tsc --noEmit", "db:migrate": "wrangler d1 execute api-upload-db --file=./migrations/0001_create_uploads_table.sql", "db:migrate:local": "wrangler d1 execute api-upload-db --local --file=./migrations/0001_create_uploads_table.sql"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250809.0", "typescript": "^5.9.2", "vitest": "^3.2.4", "vitest-environment-miniflare": "^2.14.4", "wrangler": "^4.28.1"}}