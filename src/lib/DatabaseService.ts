export interface UploadProperties {
  upload_id: string;
  workflow_instance_id: string;
  key: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

export interface UploadItem {
  id: string;
  properties: UploadProperties;
  created_at: string;
  updated_at: string;
}

export class UploadRecord {
  constructor(private readonly db: D1Database) {}

  async createUploadRecord(uploadId: string, workflowInstanceId: string, key: string): Promise<void> {
    const now = new Date().toISOString();
    const properties: UploadProperties = {
      upload_id: uploadId,
      workflow_instance_id: workflowInstanceId,
      key: key,
      status: 'pending'
    };

    await this.db.prepare(`
      INSERT INTO uploads (id, properties, created_at, updated_at)
      VALUES (?, ?, ?, ?)
    `).bind(
      crypto.randomUUID(),
      JSON.stringify(properties),
      now,
      now
    ).run();
  }

  async updateUploadStatus(uploadId: string, status: UploadProperties['status']): Promise<void> {
    const now = new Date().toISOString();

    await this.db.prepare(`
      UPDATE uploads
      SET properties = json_set(properties, '$.status', ?), updated_at = ?
      WHERE json_extract(properties, '$.upload_id') = ?
    `).bind(status, now, uploadId).run();
  }

  async updateWorkflowInstanceId(uploadId: string, workflowInstanceId: string): Promise<void> {
    const now = new Date().toISOString();

    await this.db.prepare(`
      UPDATE uploads
      SET properties = json_set(properties, '$.workflow_instance_id', ?), updated_at = ?
      WHERE json_extract(properties, '$.upload_id') = ?
    `).bind(workflowInstanceId, now, uploadId).run();
  }

  async getUploadByUploadId(uploadId: string): Promise<UploadItem | null> {
    const result = await this.db.prepare(`
      SELECT * FROM uploads WHERE json_extract(properties, '$.upload_id') = ?
    `).bind(uploadId).first();

    if (!result) return null;

    return {
      id: result.id as string,
      properties: JSON.parse(result.properties as string) as UploadProperties,
      created_at: result.created_at as string,
      updated_at: result.updated_at as string
    };
  }

  async getUploadByWorkflowInstanceId(workflowInstanceId: string): Promise<UploadItem | null> {
    const result = await this.db.prepare(`
      SELECT * FROM uploads WHERE json_extract(properties, '$.workflow_instance_id') = ?
    `).bind(workflowInstanceId).first();

    if (!result) return null;

    return {
      id: result.id as string,
      properties: JSON.parse(result.properties as string) as UploadProperties,
      created_at: result.created_at as string,
      updated_at: result.updated_at as string
    };
  }

  async getAllUploads(limit = 100, offset = 0): Promise<UploadItem[]> {
    const results = await this.db.prepare(`
      SELECT * FROM uploads
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `).bind(limit, offset).all();

    if (!results.results) return [];

    return results.results.map(row => ({
      id: row.id as string,
      properties: JSON.parse(row.properties as string) as UploadProperties,
      created_at: row.created_at as string,
      updated_at: row.updated_at as string
    }));
  }

  async deleteUpload(uploadId: string): Promise<void> {
    await this.db.prepare(`
      DELETE FROM uploads WHERE json_extract(properties, '$.upload_id') = ?
    `).bind(uploadId).run();
  }
}
