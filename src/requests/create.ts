import { BaseRequest } from './base';
import { UploadRecord } from '../lib/DatabaseService';

export class CreateRequest extends BaseRequest {
  private readonly dbService: UploadRecord;

  constructor(private readonly bucket: R2Bucket, db: D1Database, key: string) {
    super(key);
    this.dbService = new UploadRecord(db);
  }

  async execute(): Promise<Response> {
    try {
      const { key, uploadId } = await this.bucket.createMultipartUpload(this.key);

      // Create database record with pending status
      // We don't have a workflow instance ID yet, so we'll use a placeholder
      await this.dbService.createUploadRecord(uploadId, '', key);

      return this.json({ key, uploadId });
    } catch (error: any) {
      return this.error(error);
    }
  }
}
