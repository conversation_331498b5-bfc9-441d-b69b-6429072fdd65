import { BaseRequest } from './base';
import { UploadRecord } from '../lib/DatabaseService';

export class ListUploadsRequest extends BaseRequest {
  private readonly dbService: UploadRecord;

  constructor(private readonly db: D1Database, private readonly requestUrl: string) {
    super(''); // No specific key for list operation
    this.dbService = new UploadRecord(db);
  }

  async execute(): Promise<Response> {
    try {
      // Get query parameters for pagination
      const url = new URL(this.requestUrl);
      const limit = Math.min(parseInt(url.searchParams.get('limit') || '50'), 100); // Max 100
      const offset = Math.max(parseInt(url.searchParams.get('offset') || '0'), 0);

      const uploads = await this.dbService.getAllUploads(limit, offset);

      return this.json({
        uploads,
        pagination: {
          limit,
          offset,
          count: uploads.length
        }
      });
    } catch (error: any) {
      return this.error(error);
    }
  }


}
