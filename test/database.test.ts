/**
 * @vitest-environment node
 */
import { describe, it, expect, beforeEach } from 'vitest';
import { UploadRecord, UploadItem, UploadProperties } from '../src/lib/DatabaseService';

// Mock D1Database for testing
class MockD1Database {
  private data: Map<string, any> = new Map();

  prepare(query: string) {
    return {
      bind: (...params: any[]) => ({
        run: async () => ({ success: true }),
        first: async () => {
          // Simple mock implementation for testing
          if (query.includes("SELECT * FROM uploads WHERE json_extract(properties, '$.upload_id') = ?")) {
            const uploadId = params[0];
            return this.data.get(`upload_${uploadId}`) || null;
          }
          if (query.includes("SELECT * FROM uploads WHERE json_extract(properties, '$.workflow_instance_id') = ?")) {
            const workflowId = params[0];
            return this.data.get(`workflow_${workflowId}`) || null;
          }
          return null;
        },
        all: async () => ({
          results: Array.from(this.data.values()).filter(v => v.properties)
        })
      }),
      run: async () => ({ success: true }),
      first: async () => null,
      all: async () => ({ results: [] })
    };
  }

  // Mock method to set test data
  setTestData(key: string, value: any) {
    this.data.set(key, value);
  }
}

describe('UploadRecord', () => {
  let mockDb: MockD1Database;
  let dbService: UploadRecord;

  beforeEach(() => {
    mockDb = new MockD1Database();
    dbService = new UploadRecord(mockDb as any);
  });

  it('should create upload record', async () => {
    await expect(
      dbService.createUploadRecord('upload-123', 'workflow-456', 'test-file.txt')
    ).resolves.not.toThrow();
  });

  it('should update upload status', async () => {
    await expect(
      dbService.updateUploadStatus('upload-123', 'completed')
    ).resolves.not.toThrow();
  });

  it('should get upload by upload ID', async () => {
    const testUpload = {
      id: 'test-id',
      properties: '{"upload_id":"upload-123","workflow_instance_id":"workflow-456","key":"test-file.txt","status":"pending"}',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    };

    const expectedResult: UploadItem = {
      id: 'test-id',
      properties: {
        upload_id: 'upload-123',
        workflow_instance_id: 'workflow-456',
        key: 'test-file.txt',
        status: 'pending'
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    };

    mockDb.setTestData('upload_upload-123', testUpload);

    const result = await dbService.getUploadByUploadId('upload-123');
    expect(result).toEqual(expectedResult);
  });

  it('should return null for non-existent upload', async () => {
    const result = await dbService.getUploadByUploadId('non-existent');
    expect(result).toBeNull();
  });

  it('should get upload by workflow instance ID', async () => {
    const testUpload = {
      id: 'test-id',
      properties: '{"upload_id":"upload-123","workflow_instance_id":"workflow-456","key":"test-file.txt","status":"processing"}',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    };

    const expectedResult: UploadItem = {
      id: 'test-id',
      properties: {
        upload_id: 'upload-123',
        workflow_instance_id: 'workflow-456',
        key: 'test-file.txt',
        status: 'processing'
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    };

    mockDb.setTestData('workflow_workflow-456', testUpload);

    const result = await dbService.getUploadByWorkflowInstanceId('workflow-456');
    expect(result).toEqual(expectedResult);
  });

  it('should update workflow instance ID', async () => {
    await expect(
      dbService.updateWorkflowInstanceId('upload-123', 'workflow-789')
    ).resolves.not.toThrow();
  });
});
