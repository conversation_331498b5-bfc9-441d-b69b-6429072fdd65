#:schema node_modules/wrangler/config-schema.json
name = "api-upload"
account_id = "93b83c904677bf2716568cdd7c804605"
main = "src/index.ts"
compatibility_date = "2024-09-25"
workers_dev = false
send_metrics = true

[[routes]]
pattern = "*individuallist.xyz/api/upload/*"
zone_name = "individuallist.xyz"

[[r2_buckets]]
binding = "BUCKET"
bucket_name = 'public'
preview_bucket_name = 'public-dev'

[[migrations]]
new_sqlite_classes = ["Compressor"]
tag = "v1"

[[containers]]
image = "./Dockerfile"
class_name = "Compressor"
max_instances = 2

[durable_objects]
[[durable_objects.bindings]]
class_name = "Compressor"
name = "COMPRESSOR"

[[workflows]]
name = "compressor-workflow"
binding = "COMPRESSOR_WORKFLOW"
class_name = "CompressorWorkflow"

[[d1_databases]]
binding = "DB"
database_name = "api-upload-db"
database_id = "your-database-id-here"

[observability]
enabled = true

[vars]
BASE_PATH = "/api/upload"
